import 'package:flutter/material.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/models/subtask_models.dart';
import 'package:flutter_application_2/screens/widgets/common/loading_indicator.dart';
import 'package:flutter_application_2/screens/widgets/common/empty_state_widget.dart';
import 'package:get/get.dart';
import '../../controllers/task_controller.dart';
import '../../models/task_model.dart';
import '../../services/api/subtasks_api_service.dart';
import '../../services/unified_permission_service.dart';


/// علامة تبويب المهام الفرعية
/// تعرض قائمة المهام الفرعية للمهمة الحالية وتتيح إضافة وتعديل وحذف المهام الفرعية
class SubtasksTab extends StatefulWidget {
  final Task task;

  const SubtasksTab({super.key, required this.task});

  @override
  State<SubtasksTab> createState() => _SubtasksTabState();
}

class _SubtasksTabState extends State<SubtasksTab> {
  final TaskController _taskController = Get.find<TaskController>();
  final SubtasksApiService _subtasksApiService = SubtasksApiService();
  final TextEditingController _titleController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // تحميل المهام الفرعية عند بدء التشغيل
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSubtasks();
    });
  }

  /// تحميل المهام الفرعية
  Future<void> _loadSubtasks() async {
    await _taskController.loadSubtasks(widget.task.id);
  }

  /// تحديث حالة إكمال المهمة الفرعية
  Future<void> _toggleSubtaskCompletion(Subtask subtask, bool isCompleted) async {
    try {
      bool success;
      if (isCompleted) {
        success = await _subtasksApiService.completeSubtask(subtask.id);
      } else {
        success = await _subtasksApiService.uncompleteSubtask(subtask.id);
      }
      if (success) {
        await _loadSubtasks();
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في تحديث حالة المهمة الفرعية عبر الخادم',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تحديث حالة المهمة الفرعية',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // عنوان وزر إضافة
        Padding(
          padding: const EdgeInsets.all(16),
          child: LayoutBuilder(
            builder: (context, constraints) {
              // Check if we need to use a responsive layout
              final isSmallScreen = constraints.maxWidth < 400;

              if (isSmallScreen) {
                // Use a column layout for small screens
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المهام الفرعية',
                      style: AppStyles.titleMedium,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _showAddSubtaskDialog,
                        icon: const Icon(Icons.add),
                        label: const Text('إضافة مهمة فرعية'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                );
              } else {
                // Use a row layout for larger screens
                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  textDirection: TextDirection.rtl,
                  children: [
                    Flexible(
                      child: Text(
                        'المهام الفرعية',
                        style: AppStyles.titleMedium,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: _showAddSubtaskDialog,
                      icon: const Icon(Icons.add,color: Colors.white),
                      label: const Text('إضافة مهمة فرعية'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                );
              }
            },
          ),
        ),

        // قائمة المهام الفرعية
        Expanded(
          child: Obx(() {
            if (_taskController.isLoading) {
              return const Center(child: LoadingIndicator());
            }

            if (_taskController.subtasks.isEmpty) {
              return const EmptyStateWidget(
                icon: Icons.task_alt,
                message: 'لا توجد مهام فرعية\nقم بإضافة مهام فرعية لتتبع التقدم بشكل أفضل',
              );
            }

            return ListView.builder(
              itemCount: _taskController.subtasks.length,
              itemBuilder: (context, index) {
                final subtask = _taskController.subtasks[index];
                return _buildSubtaskItem(subtask);
              },
            );
          }),
        ),

        // ملخص التقدم
        Obx(() {
          final completedCount = _taskController.subtasks
              .where((s) => s.isCompleted)
              .length;
          final totalCount = _taskController.subtasks.length;

          if (totalCount == 0) {
            return const SizedBox.shrink();
          }

          final progress = completedCount / totalCount;

          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
                  blurRadius: 5,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'التقدم: $completedCount من $totalCount مكتملة',
                      style: AppStyles.bodyMedium,
                    ),
                    Text(
                      '${(progress * 100).toInt()}%',
                      style: AppStyles.titleMedium.copyWith(
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: Colors.grey.shade200,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                  minHeight: 8,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  /// بناء عنصر المهمة الفرعية
  Widget _buildSubtaskItem(Subtask subtask) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // مربع الاختيار للإكمال
                SizedBox(
                  width: 40,
                  child: Checkbox(
                    value: subtask.isCompleted,
                    activeColor: AppColors.primary,
                    onChanged: (value) {
                      if (value != null) {
                        _toggleSubtaskCompletion(subtask, value);
                      }
                    },
                  ),
                ),

                // عنوان المهمة الفرعية
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: Text(
                      subtask.title,
                      style: AppStyles.bodyMedium.copyWith(
                        decoration: subtask.isCompleted
                            ? TextDecoration.lineThrough
                            : null,
                        color: subtask.isCompleted
                            ? Colors.grey
                            : Colors.black,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ),
                ),

                // قائمة الخيارات
                SizedBox(
                  width: 40,
                  child: PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert),
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          _showEditSubtaskDialog(subtask);
                          break;
                        case 'delete':
                          _showDeleteSubtaskDialog(subtask);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      if (UnifiedPermissionService().canEditTask())
                        PopupMenuItem<String>(
                          value: 'edit',
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(Icons.edit, size: 18),
                              const SizedBox(width: 8),
                              const Text('تعديل'),
                            ],
                          ),
                        ),
                      if (UnifiedPermissionService().canDeleteTask())
                        PopupMenuItem<String>(
                          value: 'delete',
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(Icons.delete, size: 18, color: Colors.red),
                              const SizedBox(width: 8),
                              const Text('حذف', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),

            // معلومات إضافية
            Padding(
              padding: const EdgeInsets.only(right: 40, left: 16, bottom: 8),
              child: Row(
                children: [
                  const Icon(Icons.access_time, size: 14, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'تم الإنشاء: ${subtask.createdAtDateTime.day}/${subtask.createdAtDateTime.month}/${subtask.createdAtDateTime.year}',
                    style: AppStyles.labelSmall.copyWith(
                      color: Colors.grey.shade700,
                    ),
                  ),
                  if (subtask.isCompleted && subtask.completedAtDateTime != null) ...[
                    const SizedBox(width: 16),
                    const Icon(Icons.check_circle, size: 14, color: Colors.green),
                    const SizedBox(width: 4),
                    Text(
                      'مكتمل: ${subtask.completedAtDateTime!.day}/${subtask.completedAtDateTime!.month}/${subtask.completedAtDateTime!.year}',
                      style: AppStyles.labelSmall.copyWith(
                        color: Colors.green,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض مربع حوار إضافة مهمة فرعية
  void _showAddSubtaskDialog() {
    _titleController.clear();
    Get.dialog(
      Dialog(
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'إضافة مهمة فرعية',
                style: AppStyles.titleMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'العنوان *',
                  border: OutlineInputBorder(),
                ),
                maxLength: 100,
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () async {
                      if (_titleController.text.trim().isEmpty) {
                        Get.snackbar(
                          'خطأ',
                          'يرجى إدخال عنوان للمهمة الفرعية',
                          snackPosition: SnackPosition.BOTTOM,
                        );
                        return;
                      }
                      try {
                        debugPrint('محاولة إنشاء مهمة فرعية: TaskId=${widget.task.id}, Title=${_titleController.text.trim()}');
                        final result = await _subtasksApiService.createSubtask(widget.task.id, _titleController.text.trim());
                        debugPrint('تم إنشاء المهمة الفرعية بنجاح: $result');

                        // إغلاق الديالوج أولاً
                        Get.back();

                        // إعادة تحميل البيانات
                        await _loadSubtasks();

                        // عرض رسالة النجاح
                        Get.snackbar(
                          'نجح',
                          'تم إضافة المهمة الفرعية بنجاح',
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: Colors.green,
                          colorText: Colors.white,
                        );
                      } catch (e) {
                        debugPrint('❌ خطأ في إضافة المهمة الفرعية: $e');

                        // إغلاق الديالوج في جميع الحالات
                        Get.back();

                        String errorMessage = 'فشل في إضافة المهمة الفرعية';
                        Color backgroundColor = Colors.red;
                        String title = 'خطأ';

                        // تحديد نوع الخطأ لعرض رسالة أوضح
                        if (e.toString().contains('FormatException') ||
                            e.toString().contains('JSON') ||
                            e.toString().contains('type') ||
                            e.toString().contains('تحليل')) {
                          errorMessage = 'تم حفظ المهمة الفرعية بنجاح ولكن حدث خطأ في عرض البيانات. يرجى تحديث الصفحة.';
                          backgroundColor = Colors.orange;
                          title = 'تحذير';
                          debugPrint('💡 المهمة الفرعية تم حفظها في قاعدة البيانات ولكن هناك مشكلة في تحليل الاستجابة');

                          // إعادة تحميل البيانات لإظهار المهمة الفرعية الجديدة
                          await _loadSubtasks();
                        }

                        Get.snackbar(
                          title,
                          errorMessage,
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: backgroundColor,
                          colorText: Colors.white,
                          duration: const Duration(seconds: 7),
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('إضافة'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// عرض مربع حوار تعديل مهمة فرعية
  void _showEditSubtaskDialog(Subtask subtask) {
    _titleController.text = subtask.title;
    Get.dialog(
      Dialog(
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'تعديل المهمة الفرعية',
                style: AppStyles.titleMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'العنوان *',
                  border: OutlineInputBorder(),
                ),
                maxLength: 100,
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () async {
                      if (_titleController.text.trim().isEmpty) {
                        Get.snackbar(
                          'خطأ',
                          'يرجى إدخال عنوان للمهمة الفرعية',
                          snackPosition: SnackPosition.BOTTOM,
                        );
                        return;
                      }
                      try {
                        final updatedSubtask = subtask.copyWith(
                          title: _titleController.text.trim(),
                        );
                        await _subtasksApiService.updateSubtask(subtask.id, updatedSubtask);
                        await _loadSubtasks();
                        Get.back();
                        Get.snackbar(
                          'نجح',
                          'تم تعديل المهمة الفرعية بنجاح',
                          snackPosition: SnackPosition.BOTTOM,
                        );
                      } catch (e) {
                        Get.snackbar(
                          'خطأ',
                          'فشل في تعديل المهمة الفرعية',
                          snackPosition: SnackPosition.BOTTOM,
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('حفظ'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// عرض مربع حوار حذف مهمة فرعية
  void _showDeleteSubtaskDialog(Subtask subtask) {
    Get.dialog(
      AlertDialog(
        title: const Text('حذف المهمة الفرعية'),
        content: Text('هل أنت متأكد من حذف المهمة الفرعية "${subtask.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              try {
                await _subtasksApiService.deleteSubtask(subtask.id);
                await _loadSubtasks();
                Get.back();
                Get.snackbar(
                  'نجح',
                  'تم حذف المهمة الفرعية بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                );
              } catch (e) {
                Get.back();
                Get.snackbar(
                  'خطأ',
                  'فشل في حذف المهمة الفرعية',
                  snackPosition: SnackPosition.BOTTOM,
                );
              }
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
