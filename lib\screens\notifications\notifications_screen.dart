import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../controllers/notifications_controller.dart';
import '../../models/notification_models.dart';
import '../../services/notifications_service.dart';
import '../../services/unified_permission_service.dart';
// import '../../widgets/app_drawer.dart';
import '../../widgets/loading_indicator.dart';
import '../../widgets/empty_state.dart';

/// شاشة الإشعارات
class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // تهيئة إشعارات النظام (مرة واحدة فقط)
    NotificationsService.initialize(context);
    // الحصول على متحكم الإشعارات
    final controller = Get.find<NotificationsController>();
    final permissionService = Get.find<UnifiedPermissionService>();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإشعارات'),
        actions: [
          // زر تحديد الكل كمقروء
          if (permissionService.canManageNotifications())
            IconButton(
              icon: const Icon(Icons.mark_email_read),
              tooltip: 'تحديد الكل كمقروء',
              onPressed: () => _markAllAsRead(context, controller),
            ),
          // زر حذف المقروءة
          if (permissionService.canManageNotifications())
            IconButton(
              icon: const Icon(Icons.delete_sweep),
              tooltip: 'حذف المقروءة',
              onPressed: () => _deleteAllRead(context, controller),
            ),
          // زر تحديث
          if (permissionService.canViewNotifications())
            IconButton(
              icon: const Icon(Icons.refresh),
              tooltip: 'تحديث',
              onPressed: () => controller.refresh(),
            ),
        ],
      ),
      // drawer: const AppDrawer(),
      body: Column(
        children: [
          // شريط البحث والمرشحات
          _buildSearchAndFilters(context, controller),
          
          // قائمة الإشعارات
          Expanded(
            child: Obx(() {
              // عرض مؤشر التحميل
              if (controller.isLoading) {
                return const LoadingIndicator();
              }
              
              // عرض رسالة الخطأ
              // if (controller.error.isNotEmpty) {
              //   return ErrorMessage(
              //     message: controller.error,
              //     onRetry: () => controller.refresh(),
              //   );
              // }
              
              // عرض حالة فارغة
              if (controller.filteredNotifications.isEmpty) {
                return const EmptyState(
                  icon: Icons.notifications_off,
                  title: 'لا توجد إشعارات',
                  message: 'لم يتم العثور على إشعارات تطابق المرشحات الحالية',
                );
              }
              
              // عرض قائمة الإشعارات
              return RefreshIndicator(
                onRefresh: () => controller.refresh(),
                child: ListView.builder(
                  itemCount: controller.filteredNotifications.length,
                  itemBuilder: (context, index) {
                    final notification = controller.filteredNotifications[index];
                    return _buildNotificationItem(context, notification, controller);
                  },
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
  
  /// بناء شريط البحث والمرشحات
  Widget _buildSearchAndFilters(BuildContext context, NotificationsController controller) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            decoration: const InputDecoration(
              hintText: 'بحث في الإشعارات...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) => controller.setSearchQuery(value),
          ),
          
          const SizedBox(height: 8),
          
          // شريط المرشحات
          Row(
            children: [
              // مرشح النوع
              Expanded(
                child: Obx(() => Get.find<UnifiedPermissionService>().canViewNotifications()
                    ? DropdownButtonFormField<String?>(
                        decoration: const InputDecoration(
                          labelText: 'النوع',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        value: controller.typeFilter,
                        items: [
                          const DropdownMenuItem<String?>(
                            value: null,
                            child: Text('جميع الأنواع'),
                          ),
                          ...NotificationTypes.values.map((type) => DropdownMenuItem<String?>(
                            value: type,
                            child: Text(_getNotificationTypeLabel(type)),
                          )),
                        ],
                        onChanged: (value) => controller.setTypeFilter(value),
                      )
                    : const SizedBox.shrink()),
              ),
              
              const SizedBox(width: 8),
              
              // مرشح غير المقروءة فقط
              Obx(() => FilterChip(
                label: const Text('غير المقروءة فقط'),
                selected: controller.showUnreadOnly,
                onSelected: (value) => controller.setUnreadFilter(value),
              )),
              
              const SizedBox(width: 8),
              
              // زر مسح المرشحات
              if (Get.find<UnifiedPermissionService>().canManageNotifications())
                IconButton(
                  icon: const Icon(Icons.clear_all),
                  tooltip: 'مسح المرشحات',
                  onPressed: () => controller.clearFilters(),
                ),
            ],
          ),
        ],
      ),
    );
  }
  
  /// بناء عنصر إشعار
  Widget _buildNotificationItem(
    BuildContext context, 
    NotificationModel notification, 
    NotificationsController controller
  ) {
    return Dismissible(
      key: Key('notification_${notification.id}'),
      background: Container(
        color: Colors.red,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        child: const Icon(Icons.delete, color: Colors.white),
      ),
      secondaryBackground: Container(
        color: Colors.blue,
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: 20),
        child: const Icon(Icons.mark_email_read, color: Colors.white),
      ),
      confirmDismiss: (direction) async {
        if (direction == DismissDirection.startToEnd) {
          // حذف الإشعار
          return await _confirmDelete(context, notification, controller);
        } else {
          // تحديد الإشعار كمقروء
          if (!notification.isRead) {
            await controller.markAsRead(notification.id);
          }
          return false;
        }
      },
      child: Card(
        elevation: notification.isRead ? 1 : 3,
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        color: notification.isRead ? null : Colors.blue.shade50,
        child: ListTile(
          leading: _buildNotificationIcon(notification.type),
          title: Text(
            notification.title,
            style: TextStyle(
              fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(notification.content),
              const SizedBox(height: 4),
              Text(
                _formatDateTime(notification.createdAt),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          isThreeLine: true,
          trailing: !notification.isRead
              ? IconButton(
                  icon: const Icon(Icons.mark_email_read, color: Colors.blue),
                  tooltip: 'تحديد كمقروء',
                  onPressed: () => controller.markAsRead(notification.id),
                )
              : null,
          onTap: () => _onNotificationTap(context, notification, controller),
        ),
      ),
    );
  }
  
  /// عند النقر على الإشعار
  void _onNotificationTap(
    BuildContext context,
    NotificationModel notification,
    NotificationsController controller
  ) async {
    // تحديد الإشعار كمقروء إذا لم يكن مقروءاً
    if (!notification.isRead) {
      await controller.markAsRead(notification.id);
    }

    // التحقق من أن الإشعار متعلق بمهمة والتنقل المناسب
    if (_isTaskRelatedNotification(notification)) {
      _navigateToTaskDetails(notification);
    } else {
      // التحقق من أن الـ widget ما زال mounted قبل استخدام context
      if (context.mounted) {
        // عرض تفاصيل الإشعار للإشعارات غير المتعلقة بالمهام
        _showNotificationDetails(context, notification);
      }
    }
  }

  /// تحديد ما إذا كان الإشعار متعلق بمهمة
  bool _isTaskRelatedNotification(NotificationModel notification) {
    // قائمة أنواع الإشعارات المتعلقة بالمهام
    final taskRelatedTypes = [
      NotificationTypes.taskAssigned,
      NotificationTypes.taskTransferred,
      NotificationTypes.taskUpdated,
      NotificationTypes.taskCompleted,
      NotificationTypes.taskOverdue,
      NotificationTypes.taskAccessGranted,
      NotificationTypes.taskStatusChanged,
      NotificationTypes.taskPriorityChanged,
      NotificationTypes.subtaskCreated,
      NotificationTypes.subtaskUpdated,
      NotificationTypes.subtaskCompleted,
      NotificationTypes.commentAdded,
      NotificationTypes.attachmentAdded,
      NotificationTypes.attachmentDeleted,
      NotificationTypes.taskMessageReceived,
      NotificationTypes.permissionGranted,
      NotificationTypes.reminder48Hours,
      NotificationTypes.reminder24Hours,
      NotificationTypes.reminder6Hours,
    ];

    return taskRelatedTypes.contains(notification.type) &&
           notification.referenceId != null;
  }

  /// التنقل إلى تفاصيل المهمة مع التبويب المناسب
  void _navigateToTaskDetails(NotificationModel notification) {
    if (notification.referenceId == null) return;

    // 🔒 فحص الصلاحيات قبل التنقل - إصلاح ثغرة أمنية
    final permissionService = Get.find<UnifiedPermissionService>();
    if (!permissionService.canViewTaskDetails()) {
      Get.snackbar(
        'غير مسموح',
        'ليس لديك صلاحية لعرض تفاصيل المهام',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    // تحديد التبويب المناسب بناءً على نوع الإشعار
    int initialTabIndex = _getInitialTabIndex(notification.type);

    Get.toNamed('/task/detail', arguments: {
      'taskId': notification.referenceId.toString(),
      'initialTabIndex': initialTabIndex,
    });
  }

  /// تحديد فهرس التبويب الأولي بناءً على نوع الإشعار
  int _getInitialTabIndex(String notificationType) {
    switch (notificationType) {
      case NotificationTypes.commentAdded:
        return 6; // تبويب التعليقات
      case NotificationTypes.attachmentAdded:
      case NotificationTypes.attachmentDeleted:
        return 4; // تبويب الملفات
      case NotificationTypes.taskMessageReceived:
        return 7; // تبويب المحادثة
      case NotificationTypes.taskTransferred:
        return 9; // تبويب تحويلات المهمة
      case NotificationTypes.subtaskCreated:
      case NotificationTypes.subtaskUpdated:
      case NotificationTypes.subtaskCompleted:
        return 2; // تبويب المهام الفرعية
      case NotificationTypes.permissionGranted:
        return 10; // تبويب المساهمون
      default:
        return 0; // التبويب الرئيسي (النظرة الشاملة)
    }
  }
  
  /// عرض تفاصيل الإشعار
  void _showNotificationDetails(BuildContext context, NotificationModel notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(notification.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(notification.content),
            const SizedBox(height: 16),
            Text(
              'تاريخ الإنشاء: ${_formatDateTime(notification.createdAt)}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
            if (notification.referenceId != null) Text(
              'معرف المرجع: ${notification.referenceId}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            child: const Text('إغلاق'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
  
  /// تأكيد حذف الإشعار
  Future<bool> _confirmDelete(
    BuildContext context, 
    NotificationModel notification, 
    NotificationsController controller
  ) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الإشعار'),
        content: const Text('هل أنت متأكد من حذف هذا الإشعار؟'),
        actions: [
          TextButton(
            child: const Text('إلغاء'),
            onPressed: () => Navigator.of(context).pop(false),
          ),
          if (Get.find<UnifiedPermissionService>().canDeleteNotifications())
            TextButton(
              child: const Text('حذف'),
              onPressed: () => Navigator.of(context).pop(true),
            ),
        ],
      ),
    );
    
    if (result == true) {
      await controller.deleteNotification(notification.id);
      return true;
    }
    
    return false;
  }
  
  /// تحديد جميع الإشعارات كمقروءة
  void _markAllAsRead(BuildContext context, NotificationsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحديد الكل كمقروء'),
        content: const Text('هل تريد تحديد جميع الإشعارات كمقروءة؟'),
        actions: [
          TextButton(
            child: const Text('إلغاء'),
            onPressed: () => Navigator.of(context).pop(),
          ),
          if (Get.find<UnifiedPermissionService>().canManageNotifications())
            TextButton(
              child: const Text('تحديد الكل'),
              onPressed: () {
                controller.markAllAsRead();
                Navigator.of(context).pop();
              },
            ),
        ],
      ),
    );
  }
  
  /// حذف جميع الإشعارات المقروءة
  void _deleteAllRead(BuildContext context, NotificationsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الإشعارات المقروءة'),
        content: const Text('هل تريد حذف جميع الإشعارات المقروءة؟'),
        actions: [
          TextButton(
            child: const Text('إلغاء'),
            onPressed: () => Navigator.of(context).pop(),
          ),
          if (Get.find<UnifiedPermissionService>().canDeleteNotifications())
            TextButton(
              child: const Text('حذف'),
              onPressed: () {
                controller.deleteAllRead();
                Navigator.of(context).pop();
              },
            ),
        ],
      ),
    );
  }
  
  /// الحصول على أيقونة الإشعار حسب النوع
  Widget _buildNotificationIcon(String type) {
    IconData iconData;
    Color iconColor;
    
    switch (type) {
      case NotificationTypes.taskAssigned:
        iconData = Icons.assignment_ind;
        iconColor = Colors.orange;
        break;
      case NotificationTypes.taskTransferred:
        iconData = Icons.swap_horiz;
        iconColor = Colors.indigo;
        break;
      case NotificationTypes.taskUpdated:
        iconData = Icons.update;
        iconColor = Colors.blue;
        break;
      case NotificationTypes.taskCompleted:
        iconData = Icons.task_alt;
        iconColor = Colors.green;
        break;
      case NotificationTypes.taskOverdue:
        iconData = Icons.schedule;
        iconColor = Colors.red;
        break;
      case NotificationTypes.taskAccessGranted:
        iconData = Icons.lock_open;
        iconColor = Colors.cyan;
        break;
      case NotificationTypes.taskStatusChanged:
        iconData = Icons.change_circle;
        iconColor = Colors.amber;
        break;
      case NotificationTypes.taskPriorityChanged:
        iconData = Icons.priority_high;
        iconColor = Colors.deepOrange;
        break;
      case NotificationTypes.commentAdded:
        iconData = Icons.comment;
        iconColor = Colors.purple;
        break;
      case NotificationTypes.attachmentAdded:
        iconData = Icons.attach_file;
        iconColor = Colors.brown;
        break;
      case NotificationTypes.attachmentDeleted:
        iconData = Icons.delete_outline;
        iconColor = Colors.grey;
        break;
      case NotificationTypes.taskMessageReceived:
        iconData = Icons.message;
        iconColor = Colors.teal;
        break;
      case NotificationTypes.permissionGranted:
        iconData = Icons.security;
        iconColor = Colors.green;
        break;
      case NotificationTypes.reminder48Hours:
      case NotificationTypes.reminder24Hours:
      case NotificationTypes.reminder6Hours:
        iconData = Icons.alarm;
        iconColor = Colors.orange;
        break;
      case NotificationTypes.messageReceived:
        iconData = Icons.message;
        iconColor = Colors.teal;
        break;
      case NotificationTypes.systemAlert:
        iconData = Icons.warning;
        iconColor = Colors.red;
        break;
      default:
        iconData = Icons.notifications;
        iconColor = Colors.grey;
        break;
    }
    
    return CircleAvatar(
      backgroundColor: iconColor.withValues(alpha: 0.2),
      child: Icon(iconData, color: iconColor),
    );
  }
  
  /// الحصول على تسمية نوع الإشعار
  String _getNotificationTypeLabel(String type) {
    switch (type) {
      case NotificationTypes.taskAssigned:
        return 'تعيين مهمة';
      case NotificationTypes.taskTransferred:
        return 'تحويل مهمة';
      case NotificationTypes.taskUpdated:
        return 'تحديث مهمة';
      case NotificationTypes.taskCompleted:
        return 'إكمال مهمة';
      case NotificationTypes.taskOverdue:
        return 'مهمة متأخرة';
      case NotificationTypes.taskAccessGranted:
        return 'منح وصول للمهمة';
      case NotificationTypes.taskStatusChanged:
        return 'تغيير حالة المهمة';
      case NotificationTypes.taskPriorityChanged:
        return 'تغيير أولوية المهمة';
      case NotificationTypes.commentAdded:
        return 'تعليق جديد';
      case NotificationTypes.attachmentAdded:
        return 'مرفق جديد';
      case NotificationTypes.attachmentDeleted:
        return 'حذف مرفق';
      case NotificationTypes.taskMessageReceived:
        return 'رسالة مهمة';
      case NotificationTypes.permissionGranted:
        return 'منح صلاحية';
      case NotificationTypes.reminder48Hours:
        return 'تذكير 48 ساعة';
      case NotificationTypes.reminder24Hours:
        return 'تذكير 24 ساعة';
      case NotificationTypes.reminder6Hours:
        return 'تذكير 6 ساعات';
      case NotificationTypes.messageReceived:
        return 'رسالة جديدة';
      case NotificationTypes.systemAlert:
        return 'تنبيه نظام';
      default:
        return 'غير معروف';
    }
  }
  
  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays == 0) {
      // اليوم
      return 'اليوم ${DateFormat.Hm().format(dateTime)}';
    } else if (difference.inDays == 1) {
      // الأمس
      return 'الأمس ${DateFormat.Hm().format(dateTime)}';
    } else if (difference.inDays < 7) {
      // خلال الأسبوع
      return '${_getDayName(dateTime.weekday)} ${DateFormat.Hm().format(dateTime)}';
    } else {
      // تاريخ كامل
      return DateFormat('yyyy/MM/dd HH:mm').format(dateTime);
    }
  }
  
  /// الحصول على اسم اليوم
  String _getDayName(int weekday) {
    switch (weekday) {
      case 1: return 'الإثنين';
      case 2: return 'الثلاثاء';
      case 3: return 'الأربعاء';
      case 4: return 'الخميس';
      case 5: return 'الجمعة';
      case 6: return 'السبت';
      case 7: return 'الأحد';
      default: return '';
    }
  }
}