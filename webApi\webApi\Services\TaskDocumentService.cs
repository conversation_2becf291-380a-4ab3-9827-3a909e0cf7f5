using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using webApi.Models;

namespace webApi.Services
{
    /// <summary>
    /// خدمة إدارة المستندات المرتبطة بالمهام
    /// توفر تكامل كامل بين نظام المهام ونظام الأرشفة الموجود
    /// </summary>
    public class TaskDocumentService
    {
        private readonly TasksDbContext _context;
        private readonly ILogger<TaskDocumentService> _logger;
        private readonly INotificationService _notificationService;

        public TaskDocumentService(TasksDbContext context, ILogger<TaskDocumentService> logger, INotificationService notificationService)
        {
            _context = context;
            _logger = logger;
            _notificationService = notificationService;
        }

        /// <summary>
        /// الحصول على جميع مستندات مهمة محددة
        /// </summary>
        public async Task<List<TaskDocumentResponse>> GetTaskDocumentsAsync(int taskId, TaskDocumentFilter? filter = null)
        {
            try
            {
                // البحث في جدول task_documents مباشرة
                var taskDocuments = await _context.TaskDocuments
                    .Where(td => td.TaskId == taskId && !td.IsDeleted)
                    .Include(td => td.ArchiveDocument)
                        .ThenInclude(ad => ad.CreatedByNavigation)
                    .Include(td => td.ArchiveDocument)
                        .ThenInclude(ad => ad.Category)
                    .Include(td => td.Task)
                    .Include(td => td.CreatedByUser)
                    .ToListAsync();

                var responses = taskDocuments.Select(td => MapToResponse(td)).ToList();

                // تطبيق المرشحات
                if (filter != null)
                {
                    responses = ApplyFilters(responses, filter);
                }

                return responses.OrderByDescending(d => d.CreatedAt).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل مستندات المهمة {TaskId}", taskId);
                throw;
            }
        }

        /// <summary>
        /// إنشاء مستند جديد مرتبط بمهمة
        /// </summary>
        public async Task<TaskDocumentResponse?> CreateTaskDocumentAsync(CreateTaskDocumentRequest request)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                _logger.LogInformation("بدء إنشاء مستند المهمة: TaskId={TaskId}, Title={Title}, Type={Type}",
                    request.TaskId, request.Title, request.Type);

                // التحقق من صحة البيانات الأساسية
                if (string.IsNullOrWhiteSpace(request.Title))
                {
                    throw new ArgumentException("عنوان المستند مطلوب");
                }

                if (request.TaskId <= 0)
                {
                    throw new ArgumentException("معرف المهمة غير صالح");
                }

                if (request.CreatedBy <= 0)
                {
                    throw new ArgumentException("معرف المنشئ غير صالح");
                }

                // التحقق من وجود المهمة
                var task = await _context.Tasks.FindAsync(request.TaskId);
                if (task == null)
                {
                    _logger.LogWarning("المهمة غير موجودة: {TaskId}", request.TaskId);
                    throw new ArgumentException($"المهمة غير موجودة: {request.TaskId}");
                }

                _logger.LogInformation("تم العثور على المهمة: {TaskTitle}", task.Title);

                // التحقق من وجود المستخدم
                var user = await _context.Users.FindAsync(request.CreatedBy);
                if (user == null)
                {
                    _logger.LogWarning("المستخدم غير موجود: {CreatedBy}", request.CreatedBy);
                    throw new ArgumentException($"المستخدم غير موجود: {request.CreatedBy}");
                }

                _logger.LogInformation("تم العثور على المستخدم: {UserName}", user.Name);

                // إنشاء metadata للربط مع المهمة
                var metadata = CreateTaskMetadata(request);
                _logger.LogInformation("تم إنشاء metadata: {Metadata}", metadata);

                // الحصول على معرف الفئة
                var categoryId = await GetTaskDocumentsCategoryId();
                _logger.LogInformation("معرف فئة مستندات المهام: {CategoryId}", categoryId);

                // التحقق من صحة معرف الفئة
                if (!categoryId.HasValue || categoryId <= 0)
                {
                    _logger.LogWarning("لم يتم العثور على فئة صالحة، سيتم استخدام null");
                    categoryId = null;
                }

                // إنشاء مستند في نظام الأرشفة مع التحقق من صحة البيانات
                var archiveDoc = new ArchiveDocument
                {
                    Title = request.Title.Trim(),
                    Description = request.Description?.Trim(),
                    FileName = GenerateFileName(request.Title, request.Type),
                    FilePath = $"/documents/tasks/{request.TaskId}/",
                    FileType = GetFileTypeByDocumentType(request.Type),
                    FileSize = CalculateFileSize(request.Content),
                    CategoryId = categoryId,
                    Metadata = metadata,
                    Content = request.Content,
                    UploadedBy = request.CreatedBy,
                    UploadedAt = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    CreatedBy = request.CreatedBy,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    IsDeleted = false
                };

                // التحقق من صحة البيانات قبل الحفظ
                ValidateArchiveDocument(archiveDoc);

                _logger.LogInformation("إضافة مستند الأرشيف إلى قاعدة البيانات");
                _context.ArchiveDocuments.Add(archiveDoc);

                _logger.LogInformation("حفظ مستند الأرشيف في قاعدة البيانات");
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء مستند الأرشيف بنجاح: Id={ArchiveDocumentId}", archiveDoc.Id);

                // إنشاء سجل في جدول task_documents
                var taskDocument = new TaskDocument
                {
                    TaskId = request.TaskId,
                    ArchiveDocumentId = archiveDoc.Id,
                    Type = request.Type,
                    Description = request.Description,
                    CreatedBy = request.CreatedBy,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    IsDeleted = false,
                    IsShared = request.IsShared,
                    Permission = request.Permission,
                    ArchiveDocument = archiveDoc
                };

                _context.TaskDocuments.Add(taskDocument);

                _logger.LogInformation("حفظ سجل task_document في قاعدة البيانات");
                await _context.SaveChangesAsync();

                await transaction.CommitAsync();

                _logger.LogInformation("تم إنشاء سجل task_document بنجاح: TaskId={TaskId}, ArchiveDocumentId={ArchiveDocumentId}",
                    taskDocument.TaskId, taskDocument.ArchiveDocumentId);

                var response = MapToResponse(taskDocument);
                _logger.LogInformation("تم إنشاء مستند المهمة بنجاح: TaskId={TaskId}, ArchiveDocumentId={ArchiveDocumentId}",
                    response.TaskId, response.ArchiveDocumentId);

                // إرسال إشعارات إنشاء المستند
                await CreateDocumentNotifications(taskDocument, task, user);

                return response;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "خطأ في إنشاء مستند المهمة: TaskId={TaskId}, Title={Title}, Error={ErrorMessage}",
                    request.TaskId, request.Title, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// إنشاء metadata للمستند المرتبط بالمهمة
        /// </summary>
        private string CreateTaskMetadata(CreateTaskDocumentRequest request)
        {
            var metadata = new
            {
                taskId = request.TaskId.ToString(),
                type = request.Type,
                description = request.Description,
                isShared = request.IsShared,
                permission = request.Permission,
                createdForTask = true,
                createdAt = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };

            return JsonSerializer.Serialize(metadata);
        }

        /// <summary>
        /// تحويل TaskDocument إلى TaskDocumentResponse
        /// </summary>
        private TaskDocumentResponse MapToResponse(TaskDocument taskDoc)
        {
            return new TaskDocumentResponse
            {
                TaskId = taskDoc.TaskId,
                ArchiveDocumentId = taskDoc.ArchiveDocumentId,
                Type = taskDoc.Type,
                Description = taskDoc.Description,
                CreatedBy = taskDoc.CreatedBy,
                CreatedAt = taskDoc.CreatedAt,
                UpdatedAt = taskDoc.UpdatedAt,
                IsDeleted = taskDoc.IsDeleted,
                IsShared = taskDoc.IsShared,
                Permission = taskDoc.Permission,
                ArchiveDocument = taskDoc.ArchiveDocument,
                Task = taskDoc.Task,
                CreatedByUser = taskDoc.CreatedByUser
            };
        }

        /// <summary>
        /// تطبيق المرشحات على قائمة المستندات
        /// </summary>
        private List<TaskDocumentResponse> ApplyFilters(List<TaskDocumentResponse> documents, TaskDocumentFilter filter)
        {
            var filtered = documents.AsQueryable();

            if (filter.TaskId.HasValue)
                filtered = filtered.Where(d => d.TaskId == filter.TaskId.Value);

            if (!string.IsNullOrEmpty(filter.Type))
                filtered = filtered.Where(d => d.Type == filter.Type);

            if (filter.IsShared.HasValue)
                filtered = filtered.Where(d => d.IsShared == filter.IsShared.Value);

            if (!string.IsNullOrEmpty(filter.Permission))
                filtered = filtered.Where(d => d.Permission == filter.Permission);

            if (!string.IsNullOrEmpty(filter.SearchQuery))
            {
                var query = filter.SearchQuery.ToLower();
                filtered = filtered.Where(d => 
                    d.ArchiveDocument!.Title.ToLower().Contains(query) ||
                    (d.Description != null && d.Description.ToLower().Contains(query)));
            }

            if (filter.FromDate.HasValue)
                filtered = filtered.Where(d => d.CreatedAt >= filter.FromDate.Value);

            if (filter.ToDate.HasValue)
                filtered = filtered.Where(d => d.CreatedAt <= filter.ToDate.Value);

            return filtered.ToList();
        }

        /// <summary>
        /// الحصول على مستند مرتبط بمهمة بواسطة المعرف
        /// </summary>
        public async Task<TaskDocumentResponse?> GetTaskDocumentByIdAsync(int taskId, int archiveDocumentId)
        {
            try
            {
                var archiveDoc = await _context.ArchiveDocuments
                    .Include(d => d.CreatedByNavigation)
                    .Include(d => d.Category)
                    .FirstOrDefaultAsync(d => d.Id == archiveDocumentId && !d.IsDeleted);

                if (archiveDoc != null)
                {
                    var taskDoc = TaskDocument.FromArchiveMetadata(archiveDoc);
                    if (taskDoc != null && taskDoc.TaskId == taskId)
                    {
                        return MapToResponse(taskDoc);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل مستند المهمة {TaskId}-{ArchiveDocumentId}", taskId, archiveDocumentId);
                throw;
            }
        }

        /// <summary>
        /// تحديث مستند مرتبط بمهمة
        /// </summary>
        public async Task<TaskDocumentResponse?> UpdateTaskDocumentAsync(UpdateTaskDocumentRequest request)
        {
            try
            {
                var archiveDoc = await _context.ArchiveDocuments
                    .FirstOrDefaultAsync(d => d.Id == request.ArchiveDocumentId && !d.IsDeleted);

                if (archiveDoc == null)
                {
                    throw new ArgumentException($"المستند غير موجود: {request.ArchiveDocumentId}");
                }

                // تحديث metadata
                var metadata = UpdateTaskMetadata(archiveDoc.Metadata, request);

                // تحديث المستند
                if (!string.IsNullOrEmpty(request.Title))
                    archiveDoc.Title = request.Title;

                if (request.Description != null)
                    archiveDoc.Description = request.Description;

                if (!string.IsNullOrEmpty(request.Content))
                    archiveDoc.Content = request.Content;

                archiveDoc.Metadata = metadata;
                archiveDoc.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

                await _context.SaveChangesAsync();

                // تحويل إلى TaskDocument وإرجاع الاستجابة
                var taskDoc = TaskDocument.FromArchiveMetadata(archiveDoc);
                return taskDoc != null ? MapToResponse(taskDoc) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث مستند المهمة");
                throw;
            }
        }

        /// <summary>
        /// حذف مستند مرتبط بمهمة
        /// </summary>
        public async Task<bool> DeleteTaskDocumentAsync(int taskId, int archiveDocumentId)
        {
            try
            {
                var archiveDoc = await _context.ArchiveDocuments
                    .FirstOrDefaultAsync(d => d.Id == archiveDocumentId && !d.IsDeleted);

                if (archiveDoc == null)
                    return false;

                // التحقق من أن المستند مرتبط بالمهمة
                var taskDoc = TaskDocument.FromArchiveMetadata(archiveDoc);
                if (taskDoc == null || taskDoc.TaskId != taskId)
                    return false;

                // حذف منطقي
                archiveDoc.IsDeleted = true;
                archiveDoc.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف مستند المهمة");
                throw;
            }
        }

        /// <summary>
        /// البحث في مستندات المهام
        /// </summary>
        public async Task<List<TaskDocumentResponse>> SearchTaskDocumentsAsync(string query, TaskDocumentFilter? filter = null)
        {
            try
            {
                var archiveDocuments = await _context.ArchiveDocuments
                    .Where(d => !d.IsDeleted &&
                               (d.Title.Contains(query) ||
                                d.Description!.Contains(query) ||
                                d.Content!.Contains(query)))
                    .Include(d => d.CreatedByNavigation)
                    .Include(d => d.Category)
                    .ToListAsync();

                var taskDocuments = new List<TaskDocumentResponse>();

                foreach (var archiveDoc in archiveDocuments)
                {
                    var taskDoc = TaskDocument.FromArchiveMetadata(archiveDoc);
                    if (taskDoc != null)
                    {
                        var response = MapToResponse(taskDoc);
                        taskDocuments.Add(response);
                    }
                }

                // تطبيق المرشحات
                if (filter != null)
                {
                    taskDocuments = ApplyFilters(taskDocuments, filter);
                }

                return taskDocuments.OrderByDescending(d => d.CreatedAt).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن مستندات المهام");
                throw;
            }
        }

        /// <summary>
        /// الحصول على إحصائيات مستندات المهام
        /// </summary>
        public async Task<TaskDocumentStats> GetTaskDocumentStatsAsync(int taskId)
        {
            try
            {
                var documents = await GetTaskDocumentsAsync(taskId);

                return new TaskDocumentStats
                {
                    TotalDocuments = documents.Count,
                    ReportCount = documents.Count(d => d.Type == "report"),
                    AnalysisCount = documents.Count(d => d.Type == "analysis"),
                    PlanCount = documents.Count(d => d.Type == "plan"),
                    AttachmentCount = documents.Count(d => d.Type == "attachment"),
                    SharedDocuments = documents.Count(d => d.IsShared),
                    RecentDocuments = documents.Count(d =>
                        DateTimeOffset.FromUnixTimeMilliseconds(d.CreatedAt) > DateTimeOffset.UtcNow.AddDays(-7))
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل إحصائيات مستندات المهمة");
                throw;
            }
        }

        /// <summary>
        /// تحديث metadata للمستند المرتبط بالمهمة
        /// </summary>
        private string UpdateTaskMetadata(string? existingMetadata, UpdateTaskDocumentRequest request)
        {
            var metadata = new Dictionary<string, object>();

            // تحليل metadata الموجود
            if (!string.IsNullOrEmpty(existingMetadata))
            {
                try
                {
                    var existing = JsonSerializer.Deserialize<Dictionary<string, object>>(existingMetadata);
                    if (existing != null)
                        metadata = existing;
                }
                catch { }
            }

            // تحديث القيم
            metadata["taskId"] = request.TaskId.ToString();
            if (!string.IsNullOrEmpty(request.Type))
                metadata["type"] = request.Type;
            if (request.Description != null)
                metadata["description"] = request.Description;
            if (request.IsShared.HasValue)
                metadata["isShared"] = request.IsShared.Value;
            if (!string.IsNullOrEmpty(request.Permission))
                metadata["permission"] = request.Permission;

            metadata["updatedAt"] = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

            return JsonSerializer.Serialize(metadata);
        }

        /// <summary>
        /// الحصول على معرف فئة مستندات المهام
        /// </summary>
        private async Task<int?> GetTaskDocumentsCategoryId()
        {
            try
            {
                // البحث عن فئة مستندات المهام
                var category = await _context.ArchiveCategories
                    .FirstOrDefaultAsync(c => c.Name == "مستندات المهام" && !c.IsDeleted && c.IsActive);

                if (category != null)
                {
                    _logger.LogInformation("تم العثور على فئة مستندات المهام: {CategoryId}", category.Id);
                    return category.Id;
                }

                // البحث عن أي فئة موجودة لاستخدامها
                var existingCategory = await _context.ArchiveCategories
                    .Where(c => !c.IsDeleted && c.IsActive)
                    .FirstOrDefaultAsync();

                if (existingCategory != null)
                {
                    _logger.LogInformation("استخدام فئة موجودة: {CategoryName} (ID: {CategoryId})",
                        existingCategory.Name, existingCategory.Id);
                    return existingCategory.Id;
                }

                // إذا لم توجد أي فئة، إرجاع null
                _logger.LogWarning("لم يتم العثور على أي فئة، سيتم استخدام null");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على معرف فئة مستندات المهام");
                return null;
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات مستند الأرشيف
        /// </summary>
        private void ValidateArchiveDocument(ArchiveDocument document)
        {
            if (string.IsNullOrWhiteSpace(document.Title))
                throw new ArgumentException("عنوان المستند مطلوب");

            if (string.IsNullOrWhiteSpace(document.FileName))
                throw new ArgumentException("اسم الملف مطلوب");

            if (string.IsNullOrWhiteSpace(document.FilePath))
                throw new ArgumentException("مسار الملف مطلوب");

            if (string.IsNullOrWhiteSpace(document.FileType))
                throw new ArgumentException("نوع الملف مطلوب");

            if (document.UploadedBy <= 0)
                throw new ArgumentException("معرف الرافع غير صالح");

            if (document.CreatedBy <= 0)
                throw new ArgumentException("معرف المنشئ غير صالح");

            if (document.UploadedAt <= 0)
                throw new ArgumentException("تاريخ الرفع غير صالح");

            if (document.CreatedAt <= 0)
                throw new ArgumentException("تاريخ الإنشاء غير صالح");
        }

        /// <summary>
        /// إنشاء اسم ملف بناءً على العنوان والنوع
        /// </summary>
        private string GenerateFileName(string title, string type)
        {
            var cleanTitle = title.Trim().Replace(" ", "_").Replace("/", "_").Replace("\\", "_");
            var timestamp = DateTimeOffset.UtcNow.ToString("yyyyMMdd_HHmmss");
            return $"{cleanTitle}_{type}_{timestamp}.json";
        }

        /// <summary>
        /// الحصول على نوع الملف بناءً على نوع المستند
        /// </summary>
        private string GetFileTypeByDocumentType(string documentType)
        {
            return documentType.ToLower() switch
            {
                "report" => "application/json",
                "analysis" => "application/json",
                "plan" => "application/json",
                "attachment" => "application/octet-stream",
                "note" => "text/plain",
                "specification" => "application/json",
                "documentation" => "application/json",
                _ => "application/json"
            };
        }

        /// <summary>
        /// حساب حجم الملف بناءً على المحتوى
        /// </summary>
        private long CalculateFileSize(string? content)
        {
            if (string.IsNullOrEmpty(content))
                return 0;

            // تقدير تقريبي لحجم الملف (UTF-8 encoding)
            return System.Text.Encoding.UTF8.GetByteCount(content);
        }

        /// <summary>
        /// إرسال إشعارات إنشاء مستند جديد
        /// </summary>
        private async System.Threading.Tasks.Task CreateDocumentNotifications(TaskDocument taskDocument, TaskModel task, User creator)
        {
            try
            {
                // جمع معرفات المستخدمين المعنيين (باستثناء منشئ المستند)
                var userIds = new List<int>();

                // إضافة منشئ المهمة الأساسية (إذا لم يكن هو منشئ المستند)
                if (task.CreatorId != taskDocument.CreatedBy)
                {
                    userIds.Add(task.CreatorId);
                }

                // إضافة المسند له إذا كان موجوداً (وليس منشئ المستند)
                if (task.AssigneeId.HasValue &&
                    task.AssigneeId.Value != taskDocument.CreatedBy &&
                    !userIds.Contains(task.AssigneeId.Value))
                {
                    userIds.Add(task.AssigneeId.Value);
                }

                // إضافة المستخدمين الذين لهم وصول للمهمة (باستثناء منشئ المستند)
                var accessUsers = await _context.TaskAccessUsers
                    .Where(au => au.TaskId == task.Id &&
                               au.UserId != taskDocument.CreatedBy &&
                               !userIds.Contains(au.UserId))
                    .Select(au => au.UserId)
                    .ToListAsync();

                userIds.AddRange(accessUsers);

                // إرسال الإشعارات
                if (userIds.Count > 0)
                {
                    var creatorName = creator?.Name ?? "مستخدم";
                    var documentTypeName = GetDocumentTypeDisplayName(taskDocument.Type);

                    await _notificationService.CreateAndSendNotificationsAsync(
                        userIds,
                        "مستند جديد في مهمة",
                        $"المهمة رقم #{task.Id}: قام {creatorName} بإضافة مستند جديد '{taskDocument.ArchiveDocument?.Title}' ({documentTypeName}) للمهمة '{task.Title}'",
                        "document_created",
                        task.Id
                    );

                    _logger.LogInformation("تم إرسال إشعارات المستند الجديد لـ {Count} مستخدم للمهمة {TaskId}",
                        userIds.Count, task.Id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إرسال إشعارات المستند الجديد: TaskId={TaskId}, DocumentId={DocumentId}",
                    taskDocument.TaskId, taskDocument.ArchiveDocumentId);
            }
        }

        /// <summary>
        /// الحصول على اسم نوع المستند للعرض
        /// </summary>
        private string GetDocumentTypeDisplayName(string type)
        {
            return type.ToLower() switch
            {
                "report" => "تقرير",
                "attachment" => "مرفق",
                "image" => "صورة",
                "document" => "مستند",
                "spreadsheet" => "جدول بيانات",
                "presentation" => "عرض تقديمي",
                "text" => "نص",
                "other" => "أخرى",
                _ => "مستند"
            };
        }
    }
}
